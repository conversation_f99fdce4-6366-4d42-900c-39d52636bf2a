import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QTableWidget,
                               QTableWidgetItem, QVBoxLayout, QWidget,
                               QPushButton, QHeaderView, QAbstractItemView,
                               QHBoxLayout, QLabel)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QClipboard, QFont, QColor


class CustomTableWidget(QTableWidget):
    """定制化的表格组件，第一行和第一列作为表头"""

    # 定义信号
    data_copied = Signal(str)
    selection_changed = Signal(int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
        self.setup_signals()
        self.setup_styles()

    def setup_table(self):
        """初始化表格设置"""
        # 隐藏默认表头
        self.horizontalHeader().hide()
        self.verticalHeader().hide()

        # 启用选择功能
        self.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # 启用横向和纵向滚动条
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 设置交替行颜色
        self.setAlternatingRowColors(True)

        # 设置网格线
        self.setShowGrid(True)

    def setup_signals(self):
        """设置信号连接"""
        self.itemSelectionChanged.connect(self._on_selection_changed)

    def setup_styles(self):
        """设置样式"""
        # 创建表头字体
        self.header_font = QFont()
        self.header_font.setBold(True)
        self.header_font.setPointSize(10)

        # 创建普通内容字体
        self.content_font = QFont()
        self.content_font.setPointSize(9)

        # 定义颜色
        self.header_bg_color = QColor(230, 230, 250)  # 淡紫色背景
        self.header_text_color = QColor(50, 50, 50)  # 深灰色文字
        self.content_bg_color = QColor(255, 255, 255)  # 白色背景
        self.content_text_color = QColor(0, 0, 0)  # 黑色文字

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_C and event.modifiers() == Qt.ControlModifier:
            self.copy_to_clipboard()
        else:
            super().keyPressEvent(event)

    def set_data(self, data):
        """设置表格数据，第一行和第一列将作为表头

        Args:
            data: 二维列表，表格数据（包含表头）
        """
        if not data:
            return

        # 设置行列数
        self.setRowCount(len(data))
        self.setColumnCount(len(data[0]) if data else 0)

        # 填充数据并设置样式
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))

                # 设置样式
                if row == 0 or col == 0:  # 第一行或第一列（表头）
                    self._style_header_item(item)
                else:  # 普通内容
                    self._style_content_item(item)

                self.setItem(row, col, item)

        # 调整列宽
        self.auto_resize_columns()

    def _style_header_item(self, item):
        """设置表头项的样式"""
        item.setFont(self.header_font)
        item.setBackground(self.header_bg_color)
        item.setForeground(self.header_text_color)
        item.setTextAlignment(Qt.AlignCenter)

        # 表头不可编辑
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)

    def _style_content_item(self, item):
        """设置内容项的样式"""
        item.setFont(self.content_font)
        item.setBackground(self.content_bg_color)
        item.setForeground(self.content_text_color)
        item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

    def add_row(self, row_data):
        """添加一行数据（会自动处理第一列的表头样式）"""
        row_position = self.rowCount()
        self.insertRow(row_position)

        for col, value in enumerate(row_data):
            if col < self.columnCount():
                item = QTableWidgetItem(str(value))

                # 第一列作为行表头
                if col == 0:
                    self._style_header_item(item)
                else:
                    self._style_content_item(item)

                self.setItem(row_position, col, item)

    def add_column(self, column_data):
        """添加一列数据（会自动处理第一行的表头样式）"""
        col_position = self.columnCount()
        self.insertColumn(col_position)

        for row, value in enumerate(column_data):
            if row < self.rowCount():
                item = QTableWidgetItem(str(value))

                # 第一行作为列表头
                if row == 0:
                    self._style_header_item(item)
                else:
                    self._style_content_item(item)

                self.setItem(row, col_position, item)

    def remove_selected_rows(self):
        """删除选中的行（保护第一行表头）"""
        selected_rows = set()
        for item in self.selectedItems():
            if item.row() > 0:  # 不删除第一行
                selected_rows.add(item.row())

        if not selected_rows:
            self.data_copied.emit("无法删除表头行或未选中有效行")
            return

        # 从后往前删除，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            self.removeRow(row)

        self.data_copied.emit(f"已删除 {len(selected_rows)} 行")

    def remove_selected_columns(self):
        """删除选中的列（保护第一列表头）"""
        selected_cols = set()
        for item in self.selectedItems():
            if item.column() > 0:  # 不删除第一列
                selected_cols.add(item.column())

        if not selected_cols:
            self.data_copied.emit("无法删除表头列或未选中有效列")
            return

        # 从后往前删除，避免索引变化
        for col in sorted(selected_cols, reverse=True):
            self.removeColumn(col)

        self.data_copied.emit(f"已删除 {len(selected_cols)} 列")

    def get_selected_data(self, include_headers=True):
        """获取选中的数据

        Args:
            include_headers: 是否包含表头
        """
        selection = self.selectedRanges()
        if not selection:
            return []

        selected_range = selection[0]
        data = []

        start_row = selected_range.topRow()
        start_col = selected_range.leftColumn()

        # 如果不包含表头，且选择区域包含表头，则跳过表头
        if not include_headers:
            if start_row == 0:
                start_row = 1
            if start_col == 0:
                start_col = 1

        for row in range(start_row, selected_range.bottomRow() + 1):
            row_data = []
            for col in range(start_col, selected_range.rightColumn() + 1):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            if row_data:  # 只添加非空行
                data.append(row_data)

        return data

    def get_all_data(self, include_headers=True):
        """获取所有数据

        Args:
            include_headers: 是否包含表头
        """
        data = []
        start_row = 0 if include_headers else 1
        start_col = 0 if include_headers else 1

        for row in range(start_row, self.rowCount()):
            row_data = []
            for col in range(start_col, self.columnCount()):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            data.append(row_data)
        return data

    def copy_to_clipboard(self, include_headers=True):
        """复制选中的数据到剪贴板

        Args:
            include_headers: 是否包含表头
        """
        selection = self.selectedRanges()
        if not selection:
            self.data_copied.emit("没有选中数据")
            return

        selected_range = selection[0]
        copied_text = []

        for row in range(selected_range.topRow(), selected_range.bottomRow() + 1):
            row_data = []
            for col in range(selected_range.leftColumn(), selected_range.rightColumn() + 1):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            copied_text.append("\t".join(row_data))

        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(copied_text))

        rows_count = selected_range.bottomRow() - selected_range.topRow() + 1
        cols_count = selected_range.rightColumn() - selected_range.leftColumn() + 1
        message = f"已复制 {rows_count} 行 {cols_count} 列数据到剪贴板"
        self.data_copied.emit(message)

    def set_column_widths(self, widths):
        """设置列宽"""
        for col, width in enumerate(widths):
            if col < self.columnCount():
                self.setColumnWidth(col, width)

    def auto_resize_columns(self):
        """自动调整列宽"""
        self.resizeColumnsToContents()

        # 确保最小列宽
        for col in range(self.columnCount()):
            current_width = self.columnWidth(col)
            min_width = 100 if col == 0 else 80  # 第一列稍微宽一些
            if current_width < min_width:
                self.setColumnWidth(col, min_width)

    def _on_selection_changed(self):
        """选择变化时的回调"""
        selected_items = self.selectedItems()
        selected_rows = set(item.row() for item in selected_items)
        self.selection_changed.emit(len(selected_rows))


class TableDemo(QMainWindow):
    """演示如何使用CustomTableWidget的示例"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("自定义表头样式表格组件")
        self.setGeometry(100, 100, 1200, 700)

        self.setup_ui()
        self.setup_demo_data()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 状态标签
        self.status_label = QLabel("就绪 - 第一行和第一列为表头样式")
        main_layout.addWidget(self.status_label)

        # 表格
        self.table = CustomTableWidget()
        main_layout.addWidget(self.table)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 复制按钮
        copy_button = QPushButton("复制选中数据 (Ctrl+C)")
        copy_button.clicked.connect(self.table.copy_to_clipboard)
        button_layout.addWidget(copy_button)

        # 复制内容（不含表头）
        copy_content_button = QPushButton("复制内容（不含表头）")
        copy_content_button.clicked.connect(lambda: self.table.copy_to_clipboard(include_headers=False))
        button_layout.addWidget(copy_content_button)

        # 添加行按钮
        add_row_button = QPushButton("添加示例行")
        add_row_button.clicked.connect(self.add_sample_row)
        button_layout.addWidget(add_row_button)

        # 添加列按钮
        add_col_button = QPushButton("添加示例列")
        add_col_button.clicked.connect(self.add_sample_column)
        button_layout.addWidget(add_col_button)

        # 删除行按钮
        delete_row_button = QPushButton("删除选中行")
        delete_row_button.clicked.connect(self.table.remove_selected_rows)
        button_layout.addWidget(delete_row_button)

        # 删除列按钮
        delete_col_button = QPushButton("删除选中列")
        delete_col_button.clicked.connect(self.table.remove_selected_columns)
        button_layout.addWidget(delete_col_button)

        # 自动调整列宽按钮
        resize_button = QPushButton("自动调整列宽")
        resize_button.clicked.connect(self.table.auto_resize_columns)
        button_layout.addWidget(resize_button)

        main_layout.addLayout(button_layout)

        # 连接信号
        self.table.data_copied.connect(self.on_data_copied)
        self.table.selection_changed.connect(self.on_selection_changed)

    def setup_demo_data(self):
        """设置演示数据"""
        # 注意：第一行和第一列将作为表头
        data = [
            ["", "姓名", "部门", "职位", "薪资", "入职日期", "电话", "邮箱", "地址", "备注"],
            ["员工001", "张三", "技术部", "高级工程师", "15000", "2020-01-15", "13800138001", "<EMAIL>",
             "北京市朝阳区", "优秀员工"],
            ["员工002", "李四", "销售部", "销售经理", "12000", "2019-03-20", "13800138002", "<EMAIL>",
             "上海市浦东新区", "业绩突出"],
            ["员工003", "王五", "人事部", "人事专员", "8000", "2021-06-10", "13800138003", "<EMAIL>",
             "广州市天河区", "工作认真"],
            ["员工004", "赵六", "财务部", "会计师", "10000", "2020-08-05", "13800138004", "<EMAIL>",
             "深圳市南山区", "专业能力强"],
            ["员工005", "钱七", "技术部", "前端工程师", "13000", "2021-01-20", "13800138005", "<EMAIL>",
             "杭州市西湖区", "技术扎实"],
            ["员工006", "孙八", "市场部", "市场专员", "9000", "2020-11-15", "13800138006", "<EMAIL>",
             "成都市锦江区", "创意丰富"],
            ["员工007", "周九", "技术部", "后端工程师", "14000", "2019-12-01", "13800138007", "<EMAIL>",
             "武汉市武昌区", "架构能力强"],
            ["员工008", "吴十", "销售部", "销售代表", "7000", "2022-02-28", "13800138008", "<EMAIL>",
             "南京市鼓楼区", "沟通能力强"],
            ["员工009", "郑一", "运营部", "运营专员", "8500", "2021-09-10", "13800138009", "<EMAIL>",
             "西安市雁塔区", "数据敏感"],
            ["员工010", "王二", "技术部", "测试工程师", "11000", "2020-05-18", "13800138010", "<EMAIL>",
             "重庆市渝中区", "细致负责"],
        ]

        self.table.set_data(data)

        # 设置一些列的特定宽度
        column_widths = [100, 120, 100, 130, 100, 120, 130, 200, 180, 120]
        # self.table.set_column_widths(column_widths)

    def add_sample_row(self):
        """添加示例行"""
        import random
        row_count = self.table.rowCount()
        sample_data = [
            f"员工{row_count:03d}",  # 第一列作为行表头
            f"新员工{random.randint(1, 100)}",
            random.choice(["技术部", "销售部", "人事部", "财务部", "市场部", "运营部"]),
            random.choice(["工程师", "专员", "经理", "主管", "助理"]),
            f"{random.randint(6000, 20000)}",
            "2023-01-01",
            f"138{random.randint(10000000, 99999999)}",
            "<EMAIL>",
            "新地址",
            "新员工"
        ]
        self.table.add_row(sample_data)

    def add_sample_column(self):
        """添加示例列"""
        import random
        col_count = self.table.columnCount()

        # 生成列数据，第一个元素是列表头
        column_data = [f"新列{col_count}"]  # 列表头

        # 为每一行生成数据（跳过第一行，因为已经设置了列表头）
        for row in range(1, self.table.rowCount()):
            column_data.append(f"数据{random.randint(1, 100)}")

        self.table.add_column(column_data)

    def on_data_copied(self, message):
        """数据复制完成回调"""
        self.status_label.setText(f"状态: {message}")

    def on_selection_changed(self, selected_rows_count):
        """选择变化回调"""
        if selected_rows_count == 0:
            self.status_label.setText("状态: 未选中任何数据")
        else:
            self.status_label.setText(f"状态: 已选中 {selected_rows_count} 行")

def main():
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    window = TableDemo()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
