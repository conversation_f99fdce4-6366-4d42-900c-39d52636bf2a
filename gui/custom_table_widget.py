import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QTableWidget,
                               QTableWidgetItem, QVBoxLayout, QWidget,
                               QPushButton, QHeaderView, QAbstractItemView,
                               QHBoxLayout, QLabel)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QClipboard, QFont, QColor


class CustomTableWidget(QTableWidget):
    """定制化的表格组件，第一行和第一列作为表头"""

    # 定义信号
    data_copied = Signal(str)
    selection_changed = Signal(int)
    column_selected = Signal(int)  # 列选择信号，用于柱子联动

    def __init__(self, parent=None):
        super().__init__(parent)
        self._highlighted_column = -1  # 当前高亮的列
        self.setup_table()
        self.setup_signals()
        self.setup_styles()

    def setup_table(self):
        """初始化表格设置"""
        # 隐藏默认表头
        self.horizontalHeader().hide()
        self.verticalHeader().hide()

        # 启用选择功能
        self.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # 启用横向和纵向滚动条
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 设置交替行颜色
        self.setAlternatingRowColors(True)

        # 设置网格线
        self.setShowGrid(True)

    def setup_signals(self):
        """设置信号连接"""
        self.itemSelectionChanged.connect(self._on_selection_changed)

    def setup_styles(self):
        """设置样式"""
        # 创建表头字体
        self.header_font = QFont()
        self.header_font.setBold(True)
        self.header_font.setPointSize(10)

        # 创建普通内容字体
        self.content_font = QFont()
        self.content_font.setPointSize(9)

        # 定义颜色
        self.header_bg_color = QColor(230, 230, 250)  # 淡紫色背景
        self.header_text_color = QColor(50, 50, 50)  # 深灰色文字
        self.content_bg_color = QColor(255, 255, 255)  # 白色背景
        self.content_text_color = QColor(0, 0, 0)  # 黑色文字
        self.highlight_bg_color = QColor(255, 235, 59)  # 高亮列背景色（黄色）
        self.highlight_text_color = QColor(0, 0, 0)  # 高亮列文字色

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_C and event.modifiers() == Qt.ControlModifier:
            self.copy_to_clipboard()
        else:
            super().keyPressEvent(event)

    def set_data(self, data):
        """设置表格数据，第一行和第一列将作为表头

        Args:
            data: 二维列表，表格数据（包含表头）
        """
        if not data:
            return

        # 清除之前的高亮
        self._clear_column_highlight()

        # 设置行列数
        self.setRowCount(len(data))
        self.setColumnCount(len(data[0]) if data else 0)

        # 填充数据并设置样式
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))

                # 设置样式
                if row == 0 or col == 0:  # 第一行或第一列（表头）
                    self._style_header_item(item)
                else:  # 普通内容
                    self._style_content_item(item)

                self.setItem(row, col, item)

        # 调整列宽
        self.auto_resize_columns()

    def _style_header_item(self, item):
        """设置表头项的样式"""
        item.setFont(self.header_font)
        item.setBackground(self.header_bg_color)
        item.setForeground(self.header_text_color)
        item.setTextAlignment(Qt.AlignCenter)

        # 表头不可编辑
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)

    def _style_content_item(self, item):
        """设置内容项的样式"""
        item.setFont(self.content_font)
        item.setBackground(self.content_bg_color)
        item.setForeground(self.content_text_color)
        item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

    def add_row(self, row_data):
        """添加一行数据（会自动处理第一列的表头样式）"""
        row_position = self.rowCount()
        self.insertRow(row_position)

        for col, value in enumerate(row_data):
            if col < self.columnCount():
                item = QTableWidgetItem(str(value))

                # 第一列作为行表头
                if col == 0:
                    self._style_header_item(item)
                else:
                    self._style_content_item(item)

                self.setItem(row_position, col, item)

    def add_column(self, column_data):
        """添加一列数据（会自动处理第一行的表头样式）"""
        col_position = self.columnCount()
        self.insertColumn(col_position)

        for row, value in enumerate(column_data):
            if row < self.rowCount():
                item = QTableWidgetItem(str(value))

                # 第一行作为列表头
                if row == 0:
                    self._style_header_item(item)
                else:
                    self._style_content_item(item)

                self.setItem(row, col_position, item)

    def remove_selected_rows(self):
        """删除选中的行（保护第一行表头）"""
        selected_rows = set()
        for item in self.selectedItems():
            if item.row() > 0:  # 不删除第一行
                selected_rows.add(item.row())

        if not selected_rows:
            self.data_copied.emit("无法删除表头行或未选中有效行")
            return

        # 从后往前删除，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            self.removeRow(row)

        self.data_copied.emit(f"已删除 {len(selected_rows)} 行")

    def remove_selected_columns(self):
        """删除选中的列（保护第一列表头）"""
        selected_cols = set()
        for item in self.selectedItems():
            if item.column() > 0:  # 不删除第一列
                selected_cols.add(item.column())

        if not selected_cols:
            self.data_copied.emit("无法删除表头列或未选中有效列")
            return

        # 从后往前删除，避免索引变化
        for col in sorted(selected_cols, reverse=True):
            self.removeColumn(col)

        self.data_copied.emit(f"已删除 {len(selected_cols)} 列")

    def get_selected_data(self, include_headers=True):
        """获取选中的数据

        Args:
            include_headers: 是否包含表头
        """
        selection = self.selectedRanges()
        if not selection:
            return []

        selected_range = selection[0]
        data = []

        start_row = selected_range.topRow()
        start_col = selected_range.leftColumn()

        # 如果不包含表头，且选择区域包含表头，则跳过表头
        if not include_headers:
            if start_row == 0:
                start_row = 1
            if start_col == 0:
                start_col = 1

        for row in range(start_row, selected_range.bottomRow() + 1):
            row_data = []
            for col in range(start_col, selected_range.rightColumn() + 1):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            if row_data:  # 只添加非空行
                data.append(row_data)

        return data

    def get_all_data(self, include_headers=True):
        """获取所有数据

        Args:
            include_headers: 是否包含表头
        """
        data = []
        start_row = 0 if include_headers else 1
        start_col = 0 if include_headers else 1

        for row in range(start_row, self.rowCount()):
            row_data = []
            for col in range(start_col, self.columnCount()):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            data.append(row_data)
        return data

    def copy_to_clipboard(self, include_headers=True):
        """复制选中的数据到剪贴板

        Args:
            include_headers: 是否包含表头
        """
        selection = self.selectedRanges()
        if not selection:
            self.data_copied.emit("没有选中数据")
            return

        selected_range = selection[0]
        copied_text = []

        for row in range(selected_range.topRow(), selected_range.bottomRow() + 1):
            row_data = []
            for col in range(selected_range.leftColumn(), selected_range.rightColumn() + 1):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            copied_text.append("\t".join(row_data))

        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(copied_text))

        rows_count = selected_range.bottomRow() - selected_range.topRow() + 1
        cols_count = selected_range.rightColumn() - selected_range.leftColumn() + 1
        message = f"已复制 {rows_count} 行 {cols_count} 列数据到剪贴板"
        self.data_copied.emit(message)

    def set_column_widths(self, widths):
        """设置列宽"""
        for col, width in enumerate(widths):
            if col < self.columnCount():
                self.setColumnWidth(col, width)

    def auto_resize_columns(self):
        """自动调整列宽"""
        self.resizeColumnsToContents()

        # 确保最小列宽
        for col in range(self.columnCount()):
            current_width = self.columnWidth(col)
            min_width = 100 if col == 0 else 80  # 第一列稍微宽一些
            if current_width < min_width:
                self.setColumnWidth(col, min_width)

    def _on_selection_changed(self):
        """选择变化时的回调"""
        selected_items = self.selectedItems()
        selected_rows = set(item.row() for item in selected_items)
        self.selection_changed.emit(len(selected_rows))

        # 检查是否选择了某一列（排除表头行）
        selected_columns = set()
        for item in selected_items:
            if item.row() > 0 and item.column() > 0:  # 排除表头行和表头列
                selected_columns.add(item.column())

        # 如果只选择了一列，发送列选择信号
        if len(selected_columns) == 1:
            column = list(selected_columns)[0]
            self.column_selected.emit(column - 1)  # 减1是因为第一列是表头

    def highlight_column(self, column_index: int) -> None:
        """高亮指定列

        Args:
            column_index: 列索引（不包括表头列，从0开始）
        """
        # 清除之前的高亮
        self._clear_column_highlight()

        # 设置新的高亮列（+1是因为第一列是表头）
        actual_column = column_index + 1
        if actual_column < self.columnCount():
            self._highlighted_column = actual_column
            self._apply_column_highlight(actual_column)

            # 滚动到该列
            self.scrollToItem(self.item(0, actual_column), QAbstractItemView.PositionAtCenter)

    def _clear_column_highlight(self) -> None:
        """清除列高亮"""
        if self._highlighted_column >= 0:
            for row in range(self.rowCount()):
                item = self.item(row, self._highlighted_column)
                if item:
                    # 恢复原始样式
                    if row == 0 or self._highlighted_column == 0:  # 表头
                        self._style_header_item(item)
                    else:  # 普通内容
                        self._style_content_item(item)
            self._highlighted_column = -1

    def _apply_column_highlight(self, column: int) -> None:
        """应用列高亮样式"""
        for row in range(self.rowCount()):
            item = self.item(row, column)
            if item:
                item.setBackground(self.highlight_bg_color)
                item.setForeground(self.highlight_text_color)
                # 保持字体样式
                if row == 0:  # 表头行
                    item.setFont(self.header_font)
                else:  # 内容行
                    item.setFont(self.content_font)

    def set_measurement_data(self, pillars_info: list, all_measurements: dict) -> None:
        """设置量测数据表格

        Args:
            pillars_info: 柱子信息列表
            all_measurements: 所有量测结果字典 {pillar_index: MeasResult}
        """
        if not pillars_info or not all_measurements:
            return

        # 构建表格数据
        # 表头行：柱子编号
        header_row = ["量测项目"]
        for i, pillar in enumerate(pillars_info):
            pillar_id = pillar.get('pillar_id', f'柱子{i+1}')
            header_row.append(f"柱子{pillar_id}")

        # 数据行
        data_rows = []

        # 量测项目定义
        measurement_items = [
            ("柱高(μm)", "height"),
            ("TOP X(μm)", "width_x_top"),
            ("TOP Y(μm)", "width_y_top"),
            ("BTM X(μm)", "width_x_bottom"),
            ("BTM Y(μm)", "width_y_bottom")
        ]

        for item_name, item_key in measurement_items:
            row = [item_name]
            for i in range(len(pillars_info)):
                if i in all_measurements:
                    value = all_measurements[i].get(item_key, 0.0)
                    row.append(f"{value:.2f}")
                else:
                    row.append("--")
            data_rows.append(row)

        # 组合完整数据
        table_data = [header_row] + data_rows

        # 设置数据
        self.set_data(table_data)

        # 设置表格为只读
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                item = self.item(row, col)
                if item:
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
