#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型 - 管理所有应用状态
遵循Linus原则：单一数据源，消除特殊情况，通过信号槽实现松耦合
"""

import numpy as np
from PySide6.QtCore import QObject, Signal
from typing import Optional, List, Dict
from utils.daz_parser import parse_daz_file
from utils.plane_calibrator import calibrate_plane, CalibrateMetadata
from utils.pillars_detector import detect_pillars, PillarInfo
from utils.pillars_measurer import measure_pillar, MeasResult


class DataModel(QObject):
    """
    单一数据模型，管理所有应用状态
    
    Linus式设计原则：
    1. 单一数据源 - 所有状态都在这里
    2. 信号驱动 - 状态变化通过信号通知
    3. 无特殊情况 - 统一的状态管理逻辑
    """
    
    # 信号定义 - 状态变化通知
    file_loaded = Signal(str)  # 文件路径
    data_parsed = Signal()  # 解析完成
    plane_calibrated = Signal()  # 校准完成
    pillars_detected = Signal()  # 柱子检测完成
    current_pillar_changed = Signal(int)  # 当前柱子变化 (pillar_index)
    measurement_updated = Signal()  # 量测数据更新
    all_measurements_updated = Signal()  # 所有量测数据更新完成
    error_occurred = Signal(str)  # 错误信息
    
    def __init__(self):
        super().__init__()
        
        # 原始数据
        self._file_path: Optional[str] = None
        self._height_matrix: Optional[np.ndarray] = None
        self._x_step: float = 0.0
        self._y_step: float = 0.0
        
        # 校准数据
        self._calibrated_matrix: Optional[np.ndarray] = None
        self._calibration_metadata: Optional[CalibrateMetadata] = None
        
        # 柱子数据
        self._pillars_info: List[PillarInfo] = []
        self._current_pillar_index: int = -1  # -1表示未选中
        self._min_pillar_height: float = 1.0
        
        # 量测数据
        self._current_measurement: Optional[MeasResult] = None  # 保持兼容性
        self._all_measurements: Dict[int, MeasResult] = {}  # 所有柱子的量测结果缓存
    
    # === 属性访问器 ===
    @property
    def file_path(self) -> Optional[str]:
        return self._file_path
    
    @property
    def height_matrix(self) -> Optional[np.ndarray]:
        return self._height_matrix
    
    @property
    def x_step(self) -> float:
        return self._x_step
    
    @property
    def y_step(self) -> float:
        return self._y_step
    
    @property
    def calibrated_matrix(self) -> Optional[np.ndarray]:
        return self._calibrated_matrix
    
    @property
    def calibration_metadata(self) -> Optional[CalibrateMetadata]:
        return self._calibration_metadata
    
    @property
    def pillars_info(self) -> List[PillarInfo]:
        return self._pillars_info
    
    @property
    def current_pillar_index(self) -> int:
        return self._current_pillar_index
    
    @property
    def current_pillar(self) -> Optional[PillarInfo]:
        if 0 <= self._current_pillar_index < len(self._pillars_info):
            return self._pillars_info[self._current_pillar_index]
        return None
    
    @property
    def current_measurement(self) -> Optional[MeasResult]:
        return self._current_measurement

    @property
    def all_measurements(self) -> Dict[int, MeasResult]:
        """获取所有柱子的量测结果"""
        return self._all_measurements.copy()  # 返回副本，防止外部修改

    @property
    def min_pillar_height(self) -> float:
        return self._min_pillar_height
    
    @property
    def pillar_count(self) -> int:
        return len(self._pillars_info)
    
    # === 状态查询 ===
    def is_file_loaded(self) -> bool:
        return self._file_path is not None
    
    def is_data_parsed(self) -> bool:
        return self._height_matrix is not None
    
    def is_plane_calibrated(self) -> bool:
        return self._calibrated_matrix is not None
    
    def is_pillars_detected(self) -> bool:
        return len(self._pillars_info) > 0
    
    def has_current_pillar(self) -> bool:
        return self.current_pillar is not None

    def has_all_measurements(self) -> bool:
        """检查是否已完成所有柱子的量测"""
        return len(self._all_measurements) == len(self._pillars_info) and len(self._pillars_info) > 0

    def get_pillar_measurement(self, pillar_index: int) -> Optional[MeasResult]:
        """获取指定柱子的量测结果"""
        return self._all_measurements.get(pillar_index)
    
    # === 核心操作 ===
    def load_file(self, file_path: str) -> None:
        """加载文件并解析"""
        try:
            # 重置所有状态
            self._reset_all_data()
            
            # 解析文件
            height_matrix, x_step, y_step = parse_daz_file(file_path)
            
            # 更新状态
            self._file_path = file_path
            self._height_matrix = height_matrix
            self._x_step = x_step
            self._y_step = y_step
            
            # 发送信号
            self.file_loaded.emit(file_path)
            self.data_parsed.emit()
            
            # 自动执行后续步骤
            self._auto_calibrate_and_detect()
            
        except Exception as e:
            self.error_occurred.emit(f"文件加载失败: {str(e)}")
    
    def set_min_pillar_height(self, height: float) -> None:
        """设置最小柱高并重新检测"""
        if height != self._min_pillar_height:
            self._min_pillar_height = height
            if self.is_plane_calibrated():
                self._detect_pillars()
    
    def select_pillar(self, pillar_index: int) -> None:
        """选择柱子"""
        if 0 <= pillar_index < len(self._pillars_info):
            if pillar_index != self._current_pillar_index:
                self._current_pillar_index = pillar_index
                self._measure_current_pillar()
                self.current_pillar_changed.emit(pillar_index)
    
    def select_pillar_by_position(self, row: int, col: int) -> bool:
        """根据位置选择柱子（点击检测）"""
        for i, pillar in enumerate(self._pillars_info):
            bbox = pillar['bbox']
            min_row, min_col, max_row, max_col = bbox
            if min_row <= row <= max_row and min_col <= col <= max_col:
                self.select_pillar(i)
                return True
        return False
    
    def next_pillar(self) -> None:
        """切换到下一个柱子"""
        if self.pillar_count > 0:
            next_index = (self._current_pillar_index + 1) % self.pillar_count
            self.select_pillar(next_index)
    
    def previous_pillar(self) -> None:
        """切换到上一个柱子"""
        if self.pillar_count > 0:
            prev_index = (self._current_pillar_index - 1) % self.pillar_count
            self.select_pillar(prev_index)
    
    # === 私有方法 ===
    def _reset_all_data(self) -> None:
        """重置所有数据"""
        self._file_path = None
        self._height_matrix = None
        self._x_step = 0.0
        self._y_step = 0.0
        self._calibrated_matrix = None
        self._calibration_metadata = None
        self._pillars_info = []
        self._current_pillar_index = -1
        self._current_measurement = None
        self._all_measurements = {}  # 清空所有量测缓存
    
    def _auto_calibrate_and_detect(self) -> None:
        """自动执行校准和检测"""
        if not self.is_data_parsed():
            return
        
        try:
            # 校准平面
            calibrated_matrix, metadata = calibrate_plane(self._height_matrix)
            self._calibrated_matrix = calibrated_matrix
            self._calibration_metadata = metadata
            self.plane_calibrated.emit()
            
            # 检测柱子
            self._detect_pillars()
            
        except Exception as e:
            self.error_occurred.emit(f"自动处理失败: {str(e)}")
    
    def _detect_pillars(self) -> None:
        """检测柱子"""
        if not self.is_plane_calibrated():
            return

        try:
            pillars_info = detect_pillars(
                self._calibrated_matrix,
                self._x_step,
                self._y_step,
                self._min_pillar_height
            )

            self._pillars_info = pillars_info
            self._current_pillar_index = -1
            self._current_measurement = None
            self._all_measurements = {}  # 清空旧的量测缓存

            self.pillars_detected.emit()

            # 预计算所有柱子的量测数据
            if len(pillars_info) > 0:
                self._measure_all_pillars()
                # 自动选择第一个柱子
                self.select_pillar(0)

        except Exception as e:
            self.error_occurred.emit(f"柱子检测失败: {str(e)}")

    def _measure_all_pillars(self) -> None:
        """预计算所有柱子的量测数据"""
        if not self.is_plane_calibrated() or len(self._pillars_info) == 0:
            return

        try:
            for pillar_index, pillar_info in enumerate(self._pillars_info):
                measurement = measure_pillar(
                    pillar_info,
                    self._calibrated_matrix,
                    self._x_step,
                    self._y_step
                )
                self._all_measurements[pillar_index] = measurement

            # 发送所有量测完成信号
            self.all_measurements_updated.emit()

        except Exception as e:
            self.error_occurred.emit(f"批量量测失败: {str(e)}")

    def _measure_current_pillar(self) -> None:
        """量测当前柱子 - 优先使用缓存数据"""
        if not self.has_current_pillar():
            return

        try:
            # 优先使用缓存的量测结果
            if self._current_pillar_index in self._all_measurements:
                self._current_measurement = self._all_measurements[self._current_pillar_index]
            else:
                # 缓存中没有，实时计算
                measurement = measure_pillar(
                    self.current_pillar,
                    self._calibrated_matrix,
                    self._x_step,
                    self._y_step
                )
                self._current_measurement = measurement
                # 同时更新缓存
                self._all_measurements[self._current_pillar_index] = measurement

            self.measurement_updated.emit()

        except Exception as e:
            self.error_occurred.emit(f"柱子量测失败: {str(e)}")
