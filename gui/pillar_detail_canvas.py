#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
柱子局部图组件
显示当前选中柱子的bbox区域，包含高度标识和切面线
"""

import numpy as np
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt
from matplotlib.figure import Figure
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.patches as patches

from gui.data_model import DataModel


class PillarDetailCanvas(QWidget):
    """
    柱子局部图可视化组件
    
    功能：
    1. 显示当前选中柱子的bbox区域
    2. 在柱子中心显示3μm直径圆标识柱高
    3. 显示X/Y切面线（过圆心平行坐标轴）
    4. 显示"第n个/共N个"信息
    """
    
    def __init__(self, data_model: DataModel):
        super().__init__()
        self.data_model = data_model
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 柱子信息标签
        self.pillar_info_label = QLabel("")
        self.pillar_info_label.setAlignment(Qt.AlignCenter)
        self.pillar_info_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                color: #1976d2;
            }
        """)
        self.pillar_info_label.setMaximumHeight(40)
        layout.addWidget(self.pillar_info_label)
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=(6, 6))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        # 设置figure填充整个canvas
        self.figure.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)

        layout.addWidget(self.canvas, 1)  # stretch factor = 1，让canvas填充剩余空间
        
        # 初始化显示
        self._show_empty_plot()
    
    def _connect_signals(self):
        """连接数据模型信号"""
        self.data_model.current_pillar_changed.connect(self._update_plot)
        self.data_model.measurement_updated.connect(self._update_plot)
    
    def _show_empty_plot(self):
        """显示空白图"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, '请选择柱子\n查看局部详情',
                    transform=self.ax.transAxes,
                    ha='center', va='center',
                    fontsize=14, color='gray')
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.pillar_info_label.setText("")
        self.canvas.draw()
    
    def _update_plot(self):
        """更新图形显示"""
        if not self.data_model.has_current_pillar():
            self._show_empty_plot()
            return

        current_measurement = self.data_model.current_measurement
        if not current_measurement:
            self._show_empty_plot()
            return

        # 清除整个figure，重新创建subplot - 简单粗暴但有效
        self.figure.clear()
        self.ax = self.figure.add_subplot(111)
        
        # 获取数据
        pillar = self.data_model.current_pillar
        calibrated_matrix = self.data_model.calibrated_matrix
        pillar_id = pillar['pillar_id']
        center_row, center_col = pillar['center']
        bbox = pillar['bbox']
        min_row, min_col, max_row, max_col = bbox
        
        # 获取步长
        x_step = self.data_model.x_step
        y_step = self.data_model.y_step
        
        # 提取柱子区域
        pillar_region = calibrated_matrix[min_row:max_row + 1, min_col:max_col + 1]
        
        # 计算物理坐标范围（相对于柱子区域）
        region_height, region_width = pillar_region.shape
        x_extent = [0, region_width * x_step]  # 物理X坐标范围 (μm)
        y_extent = [0, region_height * y_step]  # 物理Y坐标范围 (μm)
        
        # 绘制柱子区域热力图
        im = self.ax.imshow(pillar_region, cmap='viridis',
                           aspect='equal', origin='lower',
                           extent=(x_extent[0], x_extent[1], y_extent[0], y_extent[1]))
        
        # 计算中心点在局部坐标系中的位置
        center_x_local = (center_col - min_col) * x_step
        center_y_local = (center_row - min_row) * y_step
        
        # 绘制3μm直径的圆（半径1.5μm）
        circle_radius = 1.5  # μm
        circle = patches.Circle(
            (center_x_local, center_y_local), circle_radius,
            linewidth=2, edgecolor='red', facecolor='none', alpha=0.9
        )
        self.ax.add_patch(circle)
        
        # 绘制中心点
        self.ax.plot(center_x_local, center_y_local, '+',
                    color='red', markersize=10, markeredgewidth=2)
        
        # 绘制X切面线（水平线，过圆心）
        self.ax.axhline(y=center_y_local, color='cyan', linestyle='--', 
                       linewidth=2, alpha=0.8, label='X切面')
        
        # 绘制Y切面线（垂直线，过圆心）
        self.ax.axvline(x=center_x_local, color='magenta', linestyle='--', 
                       linewidth=2, alpha=0.8, label='Y切面')
        
        # 在圆心附近标注高度值
        height_value = current_measurement['height']
        self.ax.text(center_x_local + circle_radius, center_y_local + circle_radius,
                    f'{height_value:.2f}μm',
                    fontsize=10, color='white', weight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.5))
        
        # 设置标签和标题
        self.ax.set_xlabel('X方向 (μm)')
        self.ax.set_ylabel('Y方向 (μm)')
        self.ax.set_title(f'柱子 {pillar_id} 局部图')
        
        # 添加colorbar
        self.figure.colorbar(im, ax=self.ax, label='高度 (μm)')
        
        # 添加图例
        self.ax.legend(loc='upper right', fontsize=9)
        
        # 设置网格
        self.ax.grid(True, alpha=0.3)
        
        # 更新信息标签
        current_index = self.data_model.current_pillar_index
        total_count = self.data_model.pillar_count
        self.pillar_info_label.setText(f"第{current_index+1}个/共{total_count}个")
        
        # 刷新画布 - 使用tight_layout确保图形填充
        self.figure.tight_layout()
        self.canvas.draw()
