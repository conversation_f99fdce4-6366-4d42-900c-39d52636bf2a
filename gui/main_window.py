#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口 - 简洁的界面布局
遵循Linus原则：简单布局，清晰职责分离
"""

import os
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QSplitter, QMessageBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QKeyEvent

from gui.data_model import DataModel
from gui.calibration_dialog import CalibrationDialog
from gui.pillar_detection_canvas import PillarDetectionCanvas
from gui.pillar_detail_canvas import PillarDetailCanvas
from gui.pillar_cross_section_canvas import CrossSectionCanvas
from gui.custom_table_widget import CustomTableWidget


class MainWindow(QMainWindow):
    """
    主窗口类
    
    Linus式设计：
    1. 简洁布局 - 只有必要的UI元素
    2. 拖拽优先 - 主要交互方式
    3. 键盘友好 - 支持方向键切换
    """
    
    def __init__(self):
        super().__init__()
        
        # 数据模型
        self.data_model = DataModel()
        
        # 初始化UI
        self._setup_ui()
        self._connect_signals()
        
        # 窗口设置
        self.setWindowTitle("DAZ自动测量工具")
        self.setMinimumSize(500, 400)
        self.resize(1000, 800)
        
        # 启用拖拽
        self.setAcceptDrops(True)
    
    def _setup_ui(self):
        """设置UI布局"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # === 顶部控制区域 ===
        top_layout = QHBoxLayout()
        
        # 文件路径标签
        self.file_label = QLabel("请拖入DAZ文件...")
        self.file_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 2px dashed #ccc;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #666;
            }
        """)
        self.file_label.setMinimumHeight(50)
        
        # 找平按钮
        self.calibration_button = QPushButton("查看找平结果")
        self.calibration_button.setEnabled(False)
        self.calibration_button.setMaximumWidth(120)
        self.calibration_button.setMinimumHeight(50)
        
        # 最小柱高设置
        height_layout = QHBoxLayout()
        # TODO: 抢焦点，暂隐藏
        # height_layout.addWidget(QLabel("最小柱高(μm):"))
        self.height_spinbox = QDoubleSpinBox()
        self.height_spinbox.setRange(0.0, 10.0)
        self.height_spinbox.setDecimals(1)
        self.height_spinbox.setSingleStep(0.1)
        self.height_spinbox.setValue(1.0)
        self.height_spinbox.setSuffix(" μm")
        self.height_spinbox.setMaximumWidth(90)
        # TODO: 抢焦点，暂隐藏
        # self.height_spinbox.setFocusPolicy(Qt.ClickFocus)
        # height_layout.addWidget(self.height_spinbox)
        
        top_layout.addWidget(self.file_label, 1)
        top_layout.addLayout(height_layout)
        top_layout.addWidget(self.calibration_button)
        # 设置NoFocus避免抢焦点
        self.calibration_button.setFocusPolicy(Qt.NoFocus)
        
        main_layout.addLayout(top_layout)

        # === 可视化区域 - 四宫格布局 + 表格 ===
        # 主分割器：上下分割（四宫格 + 表格）
        main_splitter = QSplitter(Qt.Vertical)

        # 四宫格区域
        four_grid_splitter = QSplitter(Qt.Vertical)

        # 上半部分：左右分割
        top_splitter = QSplitter(Qt.Horizontal)

        # 左上：柱子检测可视化
        self.pillar_canvas = PillarDetectionCanvas(self.data_model)
        top_splitter.addWidget(self.pillar_canvas)

        # 右上：柱子局部图
        self.pillar_detail_canvas = PillarDetailCanvas(self.data_model)
        top_splitter.addWidget(self.pillar_detail_canvas)

        # 设置上半部分左右比例 (1:1)
        top_splitter.setSizes([400, 400])
        four_grid_splitter.addWidget(top_splitter)

        # 下半部分：左右分割
        bottom_splitter = QSplitter(Qt.Horizontal)

        # 左下：X方向切面
        self.x_section_canvas = CrossSectionCanvas(self.data_model, "X")
        bottom_splitter.addWidget(self.x_section_canvas)

        # 右下：Y方向切面
        self.y_section_canvas = CrossSectionCanvas(self.data_model, "Y")
        bottom_splitter.addWidget(self.y_section_canvas)

        # 设置下半部分左右比例 (1:1)
        bottom_splitter.setSizes([400, 400])
        four_grid_splitter.addWidget(bottom_splitter)

        # 设置四宫格上下比例 (1:1)
        four_grid_splitter.setSizes([400, 400])
        main_splitter.addWidget(four_grid_splitter)

        # === 量测数据表格 ===
        self.measurement_table = CustomTableWidget()
        self.measurement_table.setMaximumHeight(200)  # 限制表格高度
        main_splitter.addWidget(self.measurement_table)

        # 设置主分割器比例 (四宫格:表格 = 8:3)
        main_splitter.setSizes([800, 300])

        main_layout.addWidget(main_splitter, 1)
        
        # === 状态栏 ===
        self.status_label = QLabel("就绪")
        self.statusBar().addWidget(self.status_label)
    
    def _connect_signals(self):
        """连接信号槽"""
        # 数据模型信号
        self.data_model.file_loaded.connect(self._on_file_loaded)
        self.data_model.plane_calibrated.connect(self._on_plane_calibrated)
        self.data_model.pillars_detected.connect(self._on_pillars_detected)
        self.data_model.current_pillar_changed.connect(self._on_current_pillar_changed)
        self.data_model.all_measurements_updated.connect(self._on_all_measurements_updated)
        self.data_model.error_occurred.connect(self._on_error)

        # UI控件信号
        self.calibration_button.clicked.connect(self._show_calibration_dialog)
        self.height_spinbox.valueChanged.connect(self._on_height_changed)

        # 表格信号
        self.measurement_table.column_selected.connect(self._on_table_column_selected)
    
    # === 拖拽事件处理 ===
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1:
                file_path = urls[0].toLocalFile()
                if file_path.lower().endswith(('.daz', '.da6')):
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            self.data_model.load_file(file_path)
            event.acceptProposedAction()
    
    # # === 键盘事件处理 ===
    # # 暂取消
    # def keyPressEvent(self, event: QKeyEvent):
    #     """键盘事件 - 支持方向键切换柱子"""
    #     if self.data_model.is_pillars_detected():
    #         if event.key() == Qt.Key_Left:
    #             self.data_model.previous_pillar()
    #             return
    #         elif event.key() == Qt.Key_Right:
    #             self.data_model.next_pillar()
    #             return
    #         elif event.key() == Qt.Key_Up:
    #             self.data_model.previous_pillar()
    #             return
    #         elif event.key() == Qt.Key_Down:
    #             self.data_model.next_pillar()
    #             return
    #
    #     super().keyPressEvent(event)
    
    # === 信号槽处理 ===
    def _on_file_loaded(self, file_path: str):
        """文件加载完成"""
        filename = os.path.basename(file_path)
        self.file_label.setText(f"已加载: {filename}")
        self.file_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 2px solid #4CAF50;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #2E7D32;
            }
        """)
        self.status_label.setText("文件解析中...")
    
    def _on_plane_calibrated(self):
        """平面校准完成"""
        self.calibration_button.setEnabled(True)
        self.status_label.setText("平面校准完成")
    
    def _on_pillars_detected(self):
        """柱子检测完成"""
        count = self.data_model.pillar_count
        self.status_label.setText(f"检测到 {count} 个柱子")
    
    def _on_current_pillar_changed(self, pillar_index: int):
        """当前柱子变化"""
        pillar = self.data_model.current_pillar
        if pillar:
            pillar_id = pillar['pillar_id']
            self.status_label.setText(f"正在量测柱子 {pillar_id}")
            # 高亮表格中对应的列
            self.measurement_table.highlight_column(pillar_index)
        else:
            self.status_label.setText("未选中柱子")

    def _on_all_measurements_updated(self):
        """所有量测数据更新完成"""
        # 更新表格数据
        pillars_info = self.data_model.pillars_info
        all_measurements = self.data_model.all_measurements
        self.measurement_table.set_measurement_data(pillars_info, all_measurements)
        self.status_label.setText(f"量测完成，共 {len(pillars_info)} 个柱子")

    def _on_table_column_selected(self, column_index: int):
        """表格列选择事件"""
        # 选择对应的柱子
        if 0 <= column_index < self.data_model.pillar_count:
            self.data_model.select_pillar(column_index)
    
    def _on_error(self, error_msg: str):
        """错误处理"""
        self.status_label.setText(f"错误: {error_msg}")
        QMessageBox.warning(self, "错误", error_msg)
    
    def _show_calibration_dialog(self):
        """显示校准对话框"""
        if self.data_model.is_plane_calibrated():
            dialog = CalibrationDialog(self.data_model, self)
            dialog.exec()
    
    def _on_height_changed(self, value: float):
        """最小柱高变化"""
        self.data_model.set_min_pillar_height(value)
