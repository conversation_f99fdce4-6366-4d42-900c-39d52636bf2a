#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的量测数据表格功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QVBoxLayout, QWidget, QLabel
from gui.custom_table_widget import CustomTableWidget

def test_optimized_table():
    """测试优化后的量测数据表格"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QWidget()
    window.setWindowTitle("优化后的量测数据表格测试")
    window.resize(900, 500)
    
    layout = QVBoxLayout(window)
    
    # 添加说明标签
    info_label = QLabel("""
测试功能：
1. 点击某一列选择柱子（只有明确点击才会切换，拖拽不会）
2. 拖拽选择多个单元格进行复制（不会触发柱子切换）
3. 使用Ctrl+C复制选中数据
4. 观察列高亮效果
5. 表格高度自动适配6行内容（无垂直滚动条）
    """)
    layout.addWidget(info_label)
    
    # 创建表格
    table = CustomTableWidget()
    layout.addWidget(table)
    
    # 模拟柱子信息
    pillars_info = [
        {'pillar_id': 1},
        {'pillar_id': 2},
        {'pillar_id': 3},
        {'pillar_id': 4},
        {'pillar_id': 5},
        {'pillar_id': 6},
        {'pillar_id': 7}
    ]
    
    # 模拟量测结果
    all_measurements = {}
    for i in range(7):
        all_measurements[i] = {
            'height': 2.000 + i * 0.123,
            'width_x_top': 1.200 + i * 0.111,
            'width_y_top': 1.400 + i * 0.122,
            'width_x_bottom': 1.500 + i * 0.133,
            'width_y_bottom': 1.700 + i * 0.144
        }
    
    # 设置数据
    table.set_measurement_data(pillars_info, all_measurements)
    
    # 连接信号
    def on_column_selected(column_index):
        print(f"选择了柱子 {column_index + 1}")
        table.highlight_column(column_index)
    
    table.column_selected.connect(on_column_selected)
    
    # 显示窗口
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    test_optimized_table()
